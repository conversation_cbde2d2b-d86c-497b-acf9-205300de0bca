import type { AuthResponse, LoginRequest, SignupRequest, UserInfoResponse } from '../types/api';
import { clearTokens, createBaseApi, getRefreshToken, setTokens } from './baseService';
export { refreshToken } from './tokenRefresh';

const authAPI = createBaseApi();

// Error types for better error handling
export enum AuthErrorType {
    INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
    NETWORK_ERROR = 'NETWORK_ERROR',
    TOKEN_EXPIRED = 'TOKEN_EXPIRED',
    REFRESH_FAILED = 'REFRESH_FAILED',
    UNAUTHORIZED = 'UNAUTHORIZED',
    SERVER_ERROR = 'SERVER_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class AuthError extends Error {
    type: AuthErrorType;
    originalError?: any;

    constructor(type: AuthErrorType, message: string, originalError?: any) {
        super(message);
        this.type = type;
        this.originalError = originalError;
        this.name = 'AuthError';
    }
}

// Auth functions
export const login = async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await authAPI.post<AuthResponse>('/api/login', data);
    setTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
};

export const signup = async (data: SignupRequest): Promise<AuthResponse> => {
    const response = await authAPI.post<AuthResponse>('/api/signup', data);
    setTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
};

export const logout = async (): Promise<void> => {
    const refreshToken = getRefreshToken();
    clearTokens();

    if (!refreshToken) {
        return;
    }

    try {
        await authAPI.post('/api/logout', { refresh_token: refreshToken });
    } catch (error) {
        console.error('Logout error:', error);
    }
};

export const getUserInfo = async (): Promise<UserInfoResponse> => {
    const response = await authAPI.get<UserInfoResponse>('/api/user-info');
    return response.data;
};

export const isAuthenticated = (): boolean => {
    return !!getRefreshToken();
};